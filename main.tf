
# File: main.tf (Corrected Version)

# --- Provider and Backend Configuration ---
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# --- IAM Roles and Permissions ---

# 1. IAM Role for the Lambda function, allowing it to be assumed by the Lambda service
resource "aws_iam_role" "lambda_exec_role" {
  name = "TravelBot-Lambda-Role-Terraform"

  assume_role_policy = jsonencode({
    Version   = "2012-10-17",
    Statement = [{
      Action    = "sts:AssumeRole",
      Effect    = "Allow",
      Principal = {
        Service = "lambda.amazonaws.com"
      }
    }]
  })
}

# 2. Attach the basic execution policy to the Lambda role so it can write logs to CloudWatch
resource "aws_iam_role_policy_attachment" "lambda_logs" {
  role       = aws_iam_role.lambda_exec_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# 3. IAM Role for the Lex bot, allowing it to be assumed by the Lex service
resource "aws_iam_role" "lex_service_role" {
  name = "TravelBot-Lex-Role-Terraform"

  assume_role_policy = jsonencode({
    Version   = "2012-10-17",
    Statement = [{
      Action    = "sts:AssumeRole",
      Effect    = "Allow",
      Principal = {
        Service = "lexv2.amazonaws.com"
      }
    }]
  })
}


# --- Lambda Function ---

# 1. Package the Python code from the sub-folder into a deployable .zip file
data "archive_file" "lambda_zip" {
  type        = "zip"
  source_dir  = "${path.module}/lambda_fuction"
  output_path = "${path.module}/lambda_function.zip"
}

# 2. Create the Lambda function resource itself
resource "aws_lambda_function" "get_weather_function" {
  function_name    = "getWeatherForecastFunction-Terraform"
  handler          = "get_weather.lambda_handler"
  runtime          = "python3.9"
  role             = aws_iam_role.lambda_exec_role.arn
  filename         = data.archive_file.lambda_zip.output_path
  source_code_hash = data.archive_file.lambda_zip.output_base64sha256
  timeout          = 10

  environment {
    variables = {
      API_KEY = var.openweathermap_api_key
    }
  }
}


# --- Amazon Lex V2 Bot (Corrected Resource Names) ---

# 1. Define the Bot
resource "aws_lexv2models_bot" "travelbot" {
  name                      = "TravelBot-Terraform"
  role_arn                  = aws_iam_role.lex_service_role.arn
  idle_session_ttl_in_seconds = 300
  
  data_privacy {
    child_directed = false
  }
}

# 2. Define the English (US) Locale for the Bot
resource "aws_lexv2models_bot_locale" "en_us" {
  bot_id                      = aws_lexv2models_bot.travelbot.id
  bot_version                 = "DRAFT"
  locale_id                   = "en_US"
  n_lu_intent_confidence_threshold = 0.40
}

# 3. Define the slots for the intent
resource "aws_lexv2models_slot" "city_slot" {
  name         = "city"
  bot_id       = aws_lexv2models_bot.travelbot.id
  bot_version  = aws_lexv2models_bot_locale.en_us.bot_version
  locale_id    = aws_lexv2models_bot_locale.en_us.locale_id
  intent_id    = aws_lexv2models_intent.get_weather.intent_id
  slot_type_id = "AMAZON.City"

  value_elicitation_setting {
    slot_constraint = "Required"
    
    prompt_specification {
      max_retries                = 2
      allow_interrupt           = true
      message_selection_strategy = "Random"
      
      message_group {
        message {
          plain_text_message {
            value = "For which city do you need the weather?"
          }
        }
      }
    }
  }

  depends_on = [aws_lexv2models_intent.get_weather]
}

resource "aws_lexv2models_slot" "date_slot" {
  name         = "date"
  bot_id       = aws_lexv2models_bot.travelbot.id
  bot_version  = aws_lexv2models_bot_locale.en_us.bot_version
  locale_id    = aws_lexv2models_bot_locale.en_us.locale_id
  intent_id    = aws_lexv2models_intent.get_weather.intent_id
  slot_type_id = "AMAZON.Date"

  value_elicitation_setting {
    slot_constraint = "Optional"

    prompt_specification {
      max_retries                = 2
      allow_interrupt           = true
      message_selection_strategy = "Random"

      message_group {
        message {
          plain_text_message {
            value = "And for what date?"
          }
        }
      }
    }
  }

  depends_on = [aws_lexv2models_intent.get_weather]
}

# 4. Define the 'GetWeatherForecast' Intent with Lambda fulfillment
resource "aws_lexv2models_intent" "get_weather" {
  name        = "GetWeatherForecast"
  bot_id      = aws_lexv2models_bot.travelbot.id
  bot_version = aws_lexv2models_bot_locale.en_us.bot_version
  locale_id   = aws_lexv2models_bot_locale.en_us.locale_id

  sample_utterance {
    utterance = "what is the weather in {city}"
  }
  sample_utterance {
    utterance = "what is the weather for {city} on {date}"
  }
  sample_utterance {
    utterance = "forecast for {city}"
  }
  sample_utterance {
    utterance = "tell me the weather in {city}"
  }

  # Configure Lambda fulfillment
  fulfillment_code_hook {
    enabled = true
  }

  # Ensure the locale exists before trying to create an intent in it
  depends_on = [aws_lexv2models_bot_locale.en_us]
}

# 5. Create a Bot Version from the DRAFT
resource "aws_lexv2models_bot_version" "v1" {
  bot_id      = aws_lexv2models_bot.travelbot.id
  locale_specification = {
    (aws_lexv2models_bot_locale.en_us.locale_id) = {
      source_bot_version = aws_lexv2models_bot_locale.en_us.bot_version # Point to DRAFT
    }
  }

  # Wait until the intent and slots are fully configured before creating the version
  depends_on = [
    aws_lexv2models_intent.get_weather,
    aws_lexv2models_slot.city_slot,
    aws_lexv2models_slot.date_slot
  ]
}

# 6. Configure Lambda fulfillment in the intent (since bot alias is not supported in Terraform yet)
# Note: Bot aliases are not yet supported in the Terraform AWS provider
# For now, we'll configure the Lambda function to be invoked by the bot version directly

# --- Lambda Permission ---
# Give the Lex bot permission to invoke our Lambda function
resource "aws_lambda_permission" "lex_invoke" {
  statement_id  = "AllowLexToCallLambda"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.get_weather_function.function_name
  principal     = "lexv2.amazonaws.com"
  # The source ARN references the bot version since aliases aren't supported yet
  source_arn    = "${aws_lexv2models_bot.travelbot.arn}/version/${aws_lexv2models_bot_version.v1.bot_version}"
}
